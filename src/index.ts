import 'reflect-metadata';
import { Logger } from '@/decorators/logger';
import { UserService } from '@/services/user.service';
import { ConfigService } from '@/services/config.service';

@Logger
class Application {
  private readonly userService: UserService;
  private readonly configService: ConfigService;

  constructor() {
    this.userService = new UserService();
    this.configService = new ConfigService();
  }

  public async start(): Promise<void> {
    console.log('🚀 Starting TypeScript Production Application...');
    
    const config = this.configService.getConfig();
    console.log(`📋 Environment: ${config.environment}`);
    console.log(`🔧 Debug Mode: ${config.debug}`);
    
    // Example usage of decorated service
    const users = await this.userService.getAllUsers();
    console.log(`👥 Found ${users.length} users`);
    
    const newUser = await this.userService.createUser({
      name: '<PERSON>',
      email: '<EMAIL>',
      age: 30,
    });
    console.log(`✅ Created user: ${newUser.name}`);
    
    console.log('✨ Application started successfully!');
  }
}

// Start the application
const app = new Application();
app.start().catch((error: Error) => {
  console.error('❌ Failed to start application:', error.message);
  process.exit(1);
});
