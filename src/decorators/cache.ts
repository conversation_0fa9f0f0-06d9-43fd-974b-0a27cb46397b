import 'reflect-metadata';

const CACHE_METADATA_KEY = Symbol('cache');

interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  key?: string; // Custom cache key
}

interface CacheEntry {
  value: any;
  timestamp: number;
  ttl: number;
}

class SimpleCache {
  private static instance: SimpleCache;
  private cache = new Map<string, CacheEntry>();

  public static getInstance(): SimpleCache {
    if (!SimpleCache.instance) {
      SimpleCache.instance = new SimpleCache();
    }
    return SimpleCache.instance;
  }

  public get(key: string): any {
    const entry = this.cache.get(key);
    if (!entry) {
      return undefined;
    }

    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return undefined;
    }

    return entry.value;
  }

  public set(key: string, value: any, ttl: number): void {
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl,
    });
  }

  public clear(): void {
    this.cache.clear();
  }

  public delete(key: string): boolean {
    return this.cache.delete(key);
  }
}

/**
 * Method decorator that caches method results
 */
export function Cacheable(options: CacheOptions = {}): MethodDecorator {
  return function (target: any, propertyName: string | symbol, descriptor: PropertyDescriptor): PropertyDescriptor {
    const method = descriptor.value;
    const cache = SimpleCache.getInstance();
    const defaultTtl = options.ttl || 60000; // 1 minute default

    descriptor.value = function (...args: any[]) {
      const cacheKey = options.key || `${target.constructor.name}.${String(propertyName)}.${JSON.stringify(args)}`;
      
      // Try to get from cache first
      const cachedResult = cache.get(cacheKey);
      if (cachedResult !== undefined) {
        console.log(`🎯 [Cache] Hit for ${target.constructor.name}.${String(propertyName)}`);
        return cachedResult;
      }

      console.log(`💾 [Cache] Miss for ${target.constructor.name}.${String(propertyName)}`);
      const result = method.apply(this, args);

      // Handle async methods
      if (result instanceof Promise) {
        return result.then((asyncResult: any) => {
          cache.set(cacheKey, asyncResult, defaultTtl);
          return asyncResult;
        });
      } else {
        cache.set(cacheKey, result, defaultTtl);
        return result;
      }
    };

    return descriptor;
  };
}

/**
 * Method decorator that clears cache entries
 */
export function ClearCache(cacheKeys?: string[]): MethodDecorator {
  return function (target: any, propertyName: string | symbol, descriptor: PropertyDescriptor): PropertyDescriptor {
    const method = descriptor.value;
    const cache = SimpleCache.getInstance();

    descriptor.value = function (...args: any[]) {
      const result = method.apply(this, args);

      const clearCacheEntries = (): void => {
        if (cacheKeys && cacheKeys.length > 0) {
          cacheKeys.forEach(key => {
            cache.delete(key);
            console.log(`🗑️  [Cache] Cleared cache entry: ${key}`);
          });
        } else {
          cache.clear();
          console.log(`🗑️  [Cache] Cleared all cache entries`);
        }
      };

      // Handle async methods
      if (result instanceof Promise) {
        return result.then((asyncResult: any) => {
          clearCacheEntries();
          return asyncResult;
        });
      } else {
        clearCacheEntries();
        return result;
      }
    };

    return descriptor;
  };
}
