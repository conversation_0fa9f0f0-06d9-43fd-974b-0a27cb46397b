import { AppConfig } from '@/types/config.types';
import { Cacheable } from '@/decorators/cache';
import { Timed } from '@/decorators/logger';

export class ConfigService {
  @Cacheable({ ttl: 300000 }) // Cache for 5 minutes
  @Timed
  public getConfig(): AppConfig {
    return {
      environment: (process.env.NODE_ENV as 'development' | 'production' | 'test') || 'development',
      port: parseInt(process.env.PORT || '3000', 10),
      debug: process.env.DEBUG === 'true' || process.env.NODE_ENV === 'development',
      database: {
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432', 10),
        name: process.env.DB_NAME || 'app_db',
        username: process.env.DB_USERNAME || 'postgres',
        password: process.env.DB_PASSWORD || 'password',
      },
      logging: {
        level: (process.env.LOG_LEVEL as 'error' | 'warn' | 'info' | 'debug') || 'info',
        format: (process.env.LOG_FORMAT as 'json' | 'text') || 'text',
      },
    };
  }

  @Timed
  public getEnvironment(): string {
    return this.getConfig().environment;
  }

  @Timed
  public isDevelopment(): boolean {
    return this.getConfig().environment === 'development';
  }

  @Timed
  public isProduction(): boolean {
    return this.getConfig().environment === 'production';
  }

  @Timed
  public isTest(): boolean {
    return this.getConfig().environment === 'test';
  }
}
