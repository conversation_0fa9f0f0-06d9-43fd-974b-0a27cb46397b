import 'reflect-metadata';

/**
 * Class decorator that adds logging functionality to all methods
 */
export function Logger<T extends { new (...args: any[]): {} }>(constructor: T): T {
  return class extends constructor {
    constructor(...args: any[]) {
      super(...args);
      
      // Get all method names from the prototype
      const methodNames = Object.getOwnPropertyNames(constructor.prototype)
        .filter(name => name !== 'constructor' && typeof (constructor.prototype as any)[name] === 'function');
      
      // Wrap each method with logging
      methodNames.forEach(methodName => {
        const originalMethod = (this as any)[methodName];
        if (typeof originalMethod === 'function') {
          (this as any)[methodName] = function (...methodArgs: any[]) {
            console.log(`🔍 [${constructor.name}] Calling method: ${methodName}`);
            const startTime = Date.now();
            
            try {
              const result = originalMethod.apply(this, methodArgs);
              
              // Handle async methods
              if (result instanceof Promise) {
                return result
                  .then((asyncResult: any) => {
                    const duration = Date.now() - startTime;
                    console.log(`✅ [${constructor.name}] Method ${methodName} completed in ${duration}ms`);
                    return asyncResult;
                  })
                  .catch((error: Error) => {
                    const duration = Date.now() - startTime;
                    console.error(`❌ [${constructor.name}] Method ${methodName} failed after ${duration}ms:`, error.message);
                    throw error;
                  });
              } else {
                const duration = Date.now() - startTime;
                console.log(`✅ [${constructor.name}] Method ${methodName} completed in ${duration}ms`);
                return result;
              }
            } catch (error) {
              const duration = Date.now() - startTime;
              console.error(`❌ [${constructor.name}] Method ${methodName} failed after ${duration}ms:`, (error as Error).message);
              throw error;
            }
          };
        }
      });
    }
  };
}

/**
 * Method decorator that adds performance timing to individual methods
 */
export function Timed(target: any, propertyName: string, descriptor: PropertyDescriptor): PropertyDescriptor {
  const method = descriptor.value;
  
  descriptor.value = function (...args: any[]) {
    const start = Date.now();
    console.log(`⏱️  [${target.constructor.name}] Starting ${propertyName}`);
    
    try {
      const result = method.apply(this, args);
      
      if (result instanceof Promise) {
        return result
          .then((asyncResult: any) => {
            const duration = Date.now() - start;
            console.log(`⏱️  [${target.constructor.name}] ${propertyName} completed in ${duration}ms`);
            return asyncResult;
          })
          .catch((error: Error) => {
            const duration = Date.now() - start;
            console.error(`⏱️  [${target.constructor.name}] ${propertyName} failed after ${duration}ms`);
            throw error;
          });
      } else {
        const duration = Date.now() - start;
        console.log(`⏱️  [${target.constructor.name}] ${propertyName} completed in ${duration}ms`);
        return result;
      }
    } catch (error) {
      const duration = Date.now() - start;
      console.error(`⏱️  [${target.constructor.name}] ${propertyName} failed after ${duration}ms`);
      throw error;
    }
  };
  
  return descriptor;
}

/**
 * Property decorator that adds validation to class properties
 */
export function Required(target: any, propertyName: string): void {
  let value: any;
  
  const getter = function (): any {
    return value;
  };
  
  const setter = function (newVal: any): void {
    if (newVal === null || newVal === undefined) {
      throw new Error(`Property ${propertyName} is required and cannot be null or undefined`);
    }
    value = newVal;
  };
  
  Object.defineProperty(target, propertyName, {
    get: getter,
    set: setter,
    enumerable: true,
    configurable: true,
  });
}
