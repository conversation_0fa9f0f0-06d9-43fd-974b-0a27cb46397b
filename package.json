{"name": "typescript-production-project", "version": "1.0.0", "description": "Production-ready TypeScript project with old decorator syntax", "main": "dist/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "prestart": "npm run build"}, "keywords": ["typescript", "node", "production", "decorators"], "author": "", "license": "MIT", "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "^20.6.3", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "eslint": "^8.49.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.7.0", "prettier": "^3.0.3", "rimraf": "^5.0.1", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^4.9.5"}, "dependencies": {"reflect-metadata": "^0.1.13"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}