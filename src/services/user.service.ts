import { User, CreateUserDto, UpdateUserDto, UserRepository } from '@/types/user.types';
import { Cacheable, ClearCache } from '@/decorators/cache';
import { Timed } from '@/decorators/logger';
import { generateId } from '@/utils/id.utils';

export class UserService implements UserRepository {
  private users: User[] = [];

  @Cacheable({ ttl: 30000 }) // Cache for 30 seconds
  @Timed
  public async findAll(): Promise<User[]> {
    // Simulate async operation
    await this.delay(100);
    return [...this.users];
  }

  @Cacheable({ ttl: 60000 }) // Cache for 1 minute
  @Timed
  public async findById(id: string): Promise<User | null> {
    // Simulate async operation
    await this.delay(50);
    return this.users.find(user => user.id === id) || null;
  }

  @ClearCache() // Clear all cache when creating a user
  @Timed
  public async create(userData: CreateUserDto): Promise<User> {
    // Simulate async operation
    await this.delay(200);
    
    const now = new Date();
    const user: User = {
      id: generateId(),
      name: userData.name,
      email: userData.email,
      age: userData.age,
      createdAt: now,
      updatedAt: now,
    };

    this.users.push(user);
    return user;
  }

  @ClearCache() // Clear all cache when updating a user
  @Timed
  public async update(id: string, userData: UpdateUserDto): Promise<User | null> {
    // Simulate async operation
    await this.delay(150);
    
    const userIndex = this.users.findIndex(user => user.id === id);
    if (userIndex === -1) {
      return null;
    }

    const existingUser = this.users[userIndex];
    const updatedUser: User = {
      ...existingUser,
      ...userData,
      updatedAt: new Date(),
    };

    this.users[userIndex] = updatedUser;
    return updatedUser;
  }

  @ClearCache() // Clear all cache when deleting a user
  @Timed
  public async delete(id: string): Promise<boolean> {
    // Simulate async operation
    await this.delay(100);
    
    const initialLength = this.users.length;
    this.users = this.users.filter(user => user.id !== id);
    return this.users.length < initialLength;
  }

  // Convenience method that uses the repository interface
  public async getAllUsers(): Promise<User[]> {
    return this.findAll();
  }

  public async createUser(userData: CreateUserDto): Promise<User> {
    return this.create(userData);
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
