import { UserService } from './user.service';
import { CreateUserDto, UpdateUserDto } from '@/types/user.types';

describe('UserService', () => {
  let userService: UserService;

  beforeEach(() => {
    userService = new UserService();
  });

  describe('create', () => {
    it('should create a new user', async () => {
      const userData: CreateUserDto = {
        name: '<PERSON>',
        email: '<EMAIL>',
        age: 30,
      };

      const user = await userService.create(userData);

      expect(user).toBeDefined();
      expect(user.id).toBeDefined();
      expect(user.name).toBe(userData.name);
      expect(user.email).toBe(userData.email);
      expect(user.age).toBe(userData.age);
      expect(user.createdAt).toBeInstanceOf(Date);
      expect(user.updatedAt).toBeInstanceOf(Date);
    });
  });

  describe('findAll', () => {
    it('should return empty array when no users exist', async () => {
      const users = await userService.findAll();
      expect(users).toEqual([]);
    });

    it('should return all users', async () => {
      const userData1: CreateUserDto = {
        name: 'John Doe',
        email: '<EMAIL>',
        age: 30,
      };
      const userData2: CreateUserDto = {
        name: 'Jane Smith',
        email: '<EMAIL>',
        age: 25,
      };

      await userService.create(userData1);
      await userService.create(userData2);

      const users = await userService.findAll();
      expect(users).toHaveLength(2);
    });
  });

  describe('findById', () => {
    it('should return user when found', async () => {
      const userData: CreateUserDto = {
        name: 'John Doe',
        email: '<EMAIL>',
        age: 30,
      };

      const createdUser = await userService.create(userData);
      const foundUser = await userService.findById(createdUser.id);

      expect(foundUser).toBeDefined();
      expect(foundUser?.id).toBe(createdUser.id);
    });

    it('should return null when user not found', async () => {
      const foundUser = await userService.findById('non-existent-id');
      expect(foundUser).toBeNull();
    });
  });

  describe('update', () => {
    it('should update existing user', async () => {
      const userData: CreateUserDto = {
        name: 'John Doe',
        email: '<EMAIL>',
        age: 30,
      };

      const createdUser = await userService.create(userData);
      const updateData: UpdateUserDto = {
        name: 'John Updated',
        age: 31,
      };

      const updatedUser = await userService.update(createdUser.id, updateData);

      expect(updatedUser).toBeDefined();
      expect(updatedUser?.name).toBe(updateData.name);
      expect(updatedUser?.age).toBe(updateData.age);
      expect(updatedUser?.email).toBe(userData.email); // Should remain unchanged
      expect(updatedUser?.updatedAt).not.toEqual(createdUser.updatedAt);
    });

    it('should return null when updating non-existent user', async () => {
      const updateData: UpdateUserDto = {
        name: 'Updated Name',
      };

      const result = await userService.update('non-existent-id', updateData);
      expect(result).toBeNull();
    });
  });

  describe('delete', () => {
    it('should delete existing user', async () => {
      const userData: CreateUserDto = {
        name: 'John Doe',
        email: '<EMAIL>',
        age: 30,
      };

      const createdUser = await userService.create(userData);
      const deleted = await userService.delete(createdUser.id);

      expect(deleted).toBe(true);

      const foundUser = await userService.findById(createdUser.id);
      expect(foundUser).toBeNull();
    });

    it('should return false when deleting non-existent user', async () => {
      const deleted = await userService.delete('non-existent-id');
      expect(deleted).toBe(false);
    });
  });
});
