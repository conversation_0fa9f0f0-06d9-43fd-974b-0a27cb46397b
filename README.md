# TypeScript Production Project

A production-ready TypeScript project with old decorator syntax support.

## Features

- ✅ TypeScript 4.9.5 with old decorator syntax (`experimentalDecorators: true`)
- ✅ ESLint + Prettier for code quality
- ✅ Jest for testing with coverage
- ✅ Path mapping for clean imports
- ✅ Development and production builds
- ✅ Hot reload development server
- ✅ Comprehensive npm scripts

## Quick Start

```bash
# Install dependencies
npm install

# Start development server with hot reload
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Lint code
npm run lint

# Format code
npm run format
```

## Project Structure

```
src/
├── decorators/          # Custom decorators
├── services/           # Business logic services
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
├── index.ts            # Application entry point
└── test-setup.ts       # Jest test setup
```

## Scripts

- `npm run build` - Build for production
- `npm run dev` - Start development server with hot reload
- `npm start` - Start production server
- `npm test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report
- `npm run lint` - Lint TypeScript files
- `npm run lint:fix` - Lint and fix issues
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting

## Decorator Support

This project is configured with TypeScript 4.9.5 and `experimentalDecorators: true` to support the old decorator syntax:

```typescript
@MyDecorator
class MyClass {
  @PropertyDecorator
  myProperty: string;

  @MethodDecorator
  myMethod(): void {
    // implementation
  }
}
```

## Environment Requirements

- Node.js >= 16.0.0
- npm >= 8.0.0
