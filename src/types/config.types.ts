export interface AppConfig {
  readonly environment: 'development' | 'production' | 'test';
  readonly port: number;
  readonly debug: boolean;
  readonly database: DatabaseConfig;
  readonly logging: LoggingConfig;
}

export interface DatabaseConfig {
  readonly host: string;
  readonly port: number;
  readonly name: string;
  readonly username: string;
  readonly password: string;
}

export interface LoggingConfig {
  readonly level: 'error' | 'warn' | 'info' | 'debug';
  readonly format: 'json' | 'text';
}
