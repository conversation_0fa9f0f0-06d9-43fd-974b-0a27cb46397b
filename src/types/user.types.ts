export interface User {
  readonly id: string;
  readonly name: string;
  readonly email: string;
  readonly age: number;
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

export interface CreateUserDto {
  readonly name: string;
  readonly email: string;
  readonly age: number;
}

export interface UpdateUserDto {
  readonly name?: string;
  readonly email?: string;
  readonly age?: number;
}

export interface UserRepository {
  findAll(): Promise<User[]>;
  findById(id: string): Promise<User | null>;
  create(userData: CreateUserDto): Promise<User>;
  update(id: string, userData: UpdateUserDto): Promise<User | null>;
  delete(id: string): Promise<boolean>;
}
